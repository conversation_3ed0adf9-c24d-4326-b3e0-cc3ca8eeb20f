import { info, warn, error } from './logger.js';

/**
 * 检查并返回非流式错误。
 * @param {string} responseText - 响应文本。
 * @returns {{type: 'error', content: string}|null}
 */
function getResponseError(responseText) {
  if (responseText.startsWith('<!DOCTYPE html>')) {
    return {
      type: 'error',
      content: "被cf盾了" // Blocked by Cloudflare
    };
  }
  if (responseText.startsWith('{"error"') && responseText.endsWith('}')) {
    try {
      const errorObj = JSON.parse(responseText);
      return {
        type: 'error',
        content: errorObj.error
      };
    } catch (e) {
      return null;
    }
  }
  return null;
}

/**
 * 解析流式响应，正确处理数据中包含换行符的情况。
 *
 * 响应格式示例:
 * a0:"第一行\n第二行"
 * ad:{"finishReason":"stop"}
 *
 * @param {string} buffer - 当前累积的响应字符串缓冲区。
 * @returns {{parsedItems: Array<{type: 'text' | 'done' | 'error' | 'unknown', content: any}>, remainingBuffer: string}}
 *          返回一个包含已解析项目和剩余缓冲区的对象。
 */
export function parseResponse(buffer) {
  const errorResponse = getResponseError(buffer);
  if (errorResponse) {
    return { parsedItems: [errorResponse], remainingBuffer: '' };
  }

  const parsedItems = [];
  let currentBuffer = buffer;

  while (true) {
    const newlineIndex = currentBuffer.indexOf('\n');
    if (newlineIndex === -1) {
      break;
    }

    const line = currentBuffer.substring(0, newlineIndex);
    currentBuffer = currentBuffer.substring(newlineIndex + 1);

    if (!line.trim()) {
      continue;
    }

    const colonIndex = line.indexOf(':');
    if (colonIndex === -1) {
      warn(`解析失败：行中未找到冒号 -> "${line}"`);
      continue;
    }

    const prefix = line.substring(0, colonIndex);
    const valueStr = line.substring(colonIndex + 1);

     
    try {
      const value = JSON.parse(valueStr);

      if (prefix === 'a0') {
        parsedItems.push({
          type: 'text',
          content: value
        });
      } else if (prefix === 'ad') {
        parsedItems.push({
          type: 'done',
          content: value.finishReason || 'stop'
        });
      } else {
        warn(`发现未知前缀: "${prefix}"`);
        parsedItems.push({
          type: 'unknown',
          content: value,
          prefix: prefix
        });
      }
    } catch (e) {
      warn(`JSON解析失败: "${valueStr}"`, e);
    }
  }

  return {
    parsedItems,
    remainingBuffer: currentBuffer
  };
}


// --- OpenAI 格式化工具函数 (保持不变) ---

/**
 * 创建 OpenAI 格式的流式响应块 (chunk)。
 * @param {string} content - 文本内容。
 * @param {'thinking' | 'text'} type - 内容的类型。
 * @param {string} [model] - 模型名称。
 * @returns {object}
 */
export function createStreamResponse(content, type, model) {
  return {
    id: `chatcmpl-${Date.now()}`,
    object: 'chat.completion.chunk',
    created: Math.floor(Date.now() / 1000),
    model,
    choices: [{
      index: 0,
      delta: {
        content: content || '',
        type: type || 'text',
      },
      logprobs: null,
      finish_reason: null
    }]
  };
}

/**
 * 创建 OpenAI 格式的错误响应。
 * @param {string} message - 错误消息。
 * @param {string} [model] - 模型名称。
 * @returns {object}
 */
export function createErrorResponse(message, model) {
  return {
    object: 'error',
    message,
    type: 'invalid_request_error',
    model,
  };
}

/**
 * 创建 OpenAI 格式的非流式（完整）响应。
 * @param {string} content - 完整的响应内容。
 * @param {string} [model] - 模型名称。
 * @returns {object}
 */
export function createNonStreamResponse(content, model) {
  return {
    id: `chatcmpl-${Date.now()}`,
    object: 'chat.completion',
    created: Math.floor(Date.now() / 1000),
    model,
    choices: [{
      index: 0,
      message: {
        role: 'assistant',
        content
      },
      finish_reason: 'stop'
    }],
    usage: {
      prompt_tokens: 0,
      completion_tokens: 0,
      total_tokens: 0
    }
  };
}